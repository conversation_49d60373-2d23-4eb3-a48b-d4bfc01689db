import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { supabase } from '$lib/db/supabaseClient';


export const load: PageServerLoad = async ({ params }) => {
    const { data: form } = await supabase
        .from('forms')
        .select('*')
        .eq('id', params.id)
        .eq('is_active', true)
        .single();

    if (!form) {
        throw error(404, 'Form not found');
    }

    const { data: elements } = await supabase
        .from('form_elements')
        .select('*')
        .eq('form_id', params.id)
        .order('order_position', { ascending: true });

    return {
        form,
        elements: elements || []
    };
};