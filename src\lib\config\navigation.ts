import {
	LayoutDashboard,
	Calendar,
	Users,
	FileText,
	Settings,
	LogOut,
	Plus,
	UserCheck,
	BarChart3,
	Mail,
	Smartphone,
	Share2,
	Edit,
	List,
	FileTemplate
} from 'lucide-svelte';

export interface NavigationItem {
	id: string;
	label: string;
	href?: string;
	icon?: any;
	children?: NavigationItem[];
	badge?: string;
	isActive?: boolean;
}

export const mainNavigation: NavigationItem[] = [
	{
		id: 'dashboard',
		label: 'Dashboard',
		href: '/private/dashboard',
		icon: LayoutDashboard
	},
	{
		id: 'events',
		label: 'Events',
		icon: Calendar,
		children: [
			{
				id: 'all-events',
				label: 'All Events',
				href: '/private/events'
			},
			{
				id: 'create-event',
				label: 'Create Event',
				href: '/private/events/create',
				icon: Plus
			},
			{
				id: 'event-templates',
				label: 'Event Templates',
				href: '/private/events/templates',
				icon: FileTemplate
			}
		]
	},
	{
		id: 'users',
		label: 'Users',
		icon: Users,
		children: [
			{
				id: 'all-users',
				label: 'All Users',
				href: '/private/users'
			},
			{
				id: 'admins',
				label: 'Admins',
				href: '/private/users/admins',
				icon: UserCheck
			},
			{
				id: 'user-analytics',
				label: 'User Analytics',
				href: '/private/users/analytics',
				icon: BarChart3
			}
		]
	},
	{
		id: 'forms',
		label: 'Forms',
		icon: FileText,
		children: [
			{
				id: 'all-forms',
				label: 'All Forms',
				href: '/private/form-bolt/admin/forms'
			},
			{
				id: 'create-form',
				label: 'Create Form',
				href: '/private/form-bolt/admin/forms/create',
				icon: Plus
			},
			{
				id: 'form-analytics',
				label: 'Form Analytics',
				href: '/private/form-bolt/admin/analytics',
				icon: BarChart3
			}
		]
	},
	{
		id: 'settings',
		label: 'Settings',
		icon: Settings,
		children: [
			{
				id: 'general-settings',
				label: 'General',
				href: '/private/settings'
			},
			{
				id: 'email-templates',
				label: 'Email Templates',
				href: '/private/settings/email-templates',
				icon: Mail
			},
			{
				id: 'integrations',
				label: 'Integrations',
				href: '/private/settings/integrations'
			}
		]
	}
];

export const eventNavigation = (eventId: string): NavigationItem[] => [
	{
		id: 'event-dashboard',
		label: 'Dashboard',
		href: `/private/events/${eventId}`,
		icon: LayoutDashboard
	},
	{
		id: 'event-edit',
		label: 'Edit Event',
		href: `/private/events/${eventId}/edit`,
		icon: Edit
	},
	{
		id: 'event-guestlist',
		label: 'Guest List',
		href: `/private/events/${eventId}/guestlist`,
		icon: List
	},
	{
		id: 'event-email',
		label: 'Email',
		href: `/private/events/${eventId}/email`,
		icon: Mail
	},
	{
		id: 'event-scanner',
		label: 'Scanner',
		href: `/private/events/${eventId}/scanner`,
		icon: Smartphone
	},
	{
		id: 'event-social',
		label: 'Social Share',
		href: `/private/events/${eventId}/social-share`,
		icon: Share2
	},
	{
		id: 'event-settings',
		label: 'Settings',
		href: `/private/events/${eventId}/settings`,
		icon: Settings
	}
];

// Utility function to find active navigation item
export function findActiveNavItem(pathname: string, navItems: NavigationItem[]): NavigationItem | null {
	for (const item of navItems) {
		if (item.href === pathname) {
			return item;
		}
		if (item.children) {
			const activeChild = findActiveNavItem(pathname, item.children);
			if (activeChild) {
				return activeChild;
			}
		}
	}
	return null;
}

// Utility function to check if nav item should be expanded
export function shouldExpandNavItem(pathname: string, item: NavigationItem): boolean {
	if (!item) return false;
	if (item.href === pathname) return true;
	if (item.children) {
		return item.children.some(child => shouldExpandNavItem(pathname, child));
	}
	return false;
}
