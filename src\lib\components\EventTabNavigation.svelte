<script lang="ts">
	import { page } from '$app/stores';
	import { eventNavigation } from '$lib/config/navigation';
	import type { NavigationItem } from '$lib/config/navigation';

	interface Props {
		eventId: string;
		eventTitle?: string;
	}

	let { eventId, eventTitle = 'Event' }: Props = $props();

	let tabs = $derived(eventNavigation(eventId));

	function isTabActive(tab: NavigationItem): boolean {
		return $page.url.pathname === tab.href;
	}
</script>

<div class="border-b bg-background">
	<div class="flex h-16 items-center px-6">
		<div class="flex items-center gap-4">
			<h1 class="text-lg font-semibold">{eventTitle}</h1>
			<div class="h-6 w-px bg-border"></div>
			<nav class="flex space-x-1" aria-label="Event navigation">
				{#each tabs as tab}
					<a
						href={tab.href}
						class="flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors {isTabActive(
							tab
						)
							? 'bg-primary text-primary-foreground'
							: 'text-muted-foreground hover:bg-muted hover:text-foreground'}"
						aria-current={isTabActive(tab) ? 'page' : undefined}
					>
						{#if tab.icon}
							{@const TabIcon = tab.icon}
							<TabIcon class="h-4 w-4" />
						{/if}
						<span class="hidden sm:inline">{tab.label}</span>
					</a>
				{/each}
			</nav>
		</div>
	</div>
</div>
