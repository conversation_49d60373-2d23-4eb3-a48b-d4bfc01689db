<script lang="ts">
	import { Globe, Loader } from 'lucide-svelte';
	import { page } from '$app/stores';
	import { superForm } from 'sveltekit-superforms';
	import toast, { Toaster } from 'svelte-hot-french-toast';
	import { formatDate, formatTime } from '$lib/utils/formatdatetime.js';
	import Footer from '$lib/components/Footer.svelte';
	import { Turnstile } from 'svelte-turnstile';
	import { PUBLIC_SITE_KEY_LOCALHOST } from '$env/static/public';
	import { PUBLIC_SITE_KEY } from '$env/static/public';
	import { redirect } from '@sveltejs/kit';
	import { goto } from '$app/navigation';
	import Markdown from '$lib/components/Markdown.svelte';

	export let data;

	let turnstileToken: boolean;

	let agreed = false;
	const { form, errors, constraints, message, enhance, delayed } = superForm(data.form, {
		onResult: async ({ result }) => {
			// Check if form submission was successful
			// 	console.log('Result from Form: ', result);
			// 	if (result.type === 'success') {
			// 		const redirectUrl = `/`;
			// 		goto('/', { replaceState: true });
			// 		console.log('Redirection code all ready run....');
			// 		// return;
			// 	}
			// }

			console.log('Form submission result:', result);

			if (result.type === 'success') {
				const redirectUrl = `/${$page.params.slug}/thankyou?email=${$form.email}&registered=true`;
				console.log('Attempting to redirect to:', redirectUrl);

				try {
					await goto(redirectUrl);
					console.log('Redirect should have happened');
				} catch (error) {
					console.error('Redirect failed:', error);
				}
			}
		},
		onError: (err) => {
			console.error('Form submission error:', err);
		},
		onSubmit: () => {
			console.log('Form is being submitted');
		}
	});

	// Success / Fail message Tost based on Form Response
	// if ($message === 'success') {
	// 	toast.success('Successfully Registered. Please check your email for confirmation.', {
	// 		duration: 10000,
	// 		position: 'bottom-center'
	// 	});
	// } else

	$: if (data.form.message === 'fail') {
		toast.error('Please try again later');
	}

	// Handle Turnstile verification
	function handleTurnstileVerification(event: CustomEvent<{ token: string }>) {
		turnstileToken = true;
	}

	function handleTurnstileError(event: CustomEvent<{ error: string }>) {
		turnstileToken = false;
		toast.error('Verification failed. Please try again.');
	}

	// Determine if form can be submitted
	$: isFormSubmittable =
		agreed &&
		turnstileToken !== null &&
		$form.firstName &&
		$form.lastName &&
		$form.email &&
		$form.mobile &&
		// $form.organization &&
		$form.gender &&
		// $form.nationality &&
		$form.business &&
		$form.category &&
		$form.stage &&
		$form.know;

	// Define your page metadata here
	const pageTitle = `${data.event?.title} | eHaris Events`;
	const pageDescription = `Register for ${data.event?.title}`;
	const siteName = 'eventsdemo.eHaris.com';

	const title = `${data.event?.title}`;
	const description = `${data.event?.short_description}`;
	const image = data.event?.image_url;
</script>

<svelte:head>
	<title>{pageTitle}</title>
	<meta name="description" content={pageDescription} />

	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website" />
	<meta property="og:url" content={$page.url.href} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={image} />

	<!-- Twitter -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:url" content={$page.url.href} />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={image} />

	<!-- WhatsApp -->
	<meta property="og:site_name" content={siteName} />
	<meta property="og:locale" content="en_US" />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
</svelte:head>

<main class="mx-auto max-w-4xl px-4 py-8">
	<div class="mb-8">
		<img
			src={data.event?.image_url}
			alt={data.event?.title}
			class="h-auto w-full rounded-lg object-cover shadow-lg"
		/>
	</div>

	<h1 class="mb-4 text-3xl font-bold">{data.event?.title}</h1>

	<div class="mb-6 text-gray-600">
		<p class="mb-2">
			<strong>Date:</strong>
			{formatDate(data.event?.start_date)} - {formatDate(data.event?.end_date)}
		</p>
		<p class=" mb-2 flex">
			<strong>Time:</strong>
			<span class="ml-1"
				>{formatTime(data.event?.start_time)} - {formatTime(data.event?.end_time)}</span
			> <span class="ml-3 flex items-center"><Globe size="15" /> +3:00 GMT</span>
		</p>
		<p>
			<strong>Location:</strong>
			{data.event?.location} -
			<a class="text-blue-500" href={data.event?.location_url} target="_blank">View Map</a>
		</p>
	</div>

	<h2 class="mb-4 text-2xl font-semibold">About the Event</h2>

	<!-- <p class="mb-6 text-justify text-gray-700">{data.event?.long_description}</p> -->
	<!-- <div class="event-description">
		<Markdown content={data.event?.long_description} />
	</div> -->
	<!-- English description with LTR -->
	<div class="event-description mb-6">
		<Markdown content={data.event?.long_description} />
	</div>
	<br />
	<!-- <p class="mb-6 text-justify text-gray-700">{data.event?.long_description_arabic}</p> -->
	<!-- <div class="event-description">
		<Markdown content={data.event?.long_description_arabic} />
	</div> -->
	<!-- Arabic description with RTL -->
	{#if data.event?.long_description_arabic}
		<div class="event-description mb-6">
			<Markdown content={data.event?.long_description_arabic} isRTL={true} />
		</div>
	{/if}

	{#if data.waitingList}
		<!-- Warning -->
		<div class="flex justify-between rounded bg-yellow-400 px-8 py-6 text-black">
			<div class="flex items-center">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="mr-6 h-7 w-7"
					viewBox="0 0 20 20"
					fill="currentColor"
				>
					<path
						d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"
					/></svg
				>
				<p>Registration is Full. Waiting List is enabled</p>
			</div>
		</div>
	{/if}

	<!-- Form  -->
	<div>
		<center>
			<h2 class="text-2xl font-bold">Registration Form</h2>
		</center>

		<form class="px-6 py-4 text-sm md:text-base" method="POST" use:enhance>
			<input type="hidden" id="eventId" name="eventId" value={data.event?.id} />

			<!-- Name -->
			<!-- <div class="mb-4">
				<label class="mb-2 block font-semibold text-gray-700" for="name">
					<div class="flex items-center justify-between">
						<span>Name <span class="text-red-500">*</span></span>
						<span>الإسم <span class="text-red-500">*</span></span>
					</div>
				</label>
				<input
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					id="name"
					name="name"
					aria-invalid={$errors.name ? 'true' : undefined}
					type="text"
					bind:value={$form.name}
					{...$constraints.name}
					placeholder="Enter your name"
					required
				/>
				{#if $errors.name}<span class="invalid">{$errors.name}</span>{/if}
			</div> -->

			<!-- First Name -->
			<div class="mb-4">
				<label class="mb-2 block font-semibold text-gray-700" for="firstName">
					<div class="flex items-center justify-between">
						<span>First Name <span class="text-red-500">*</span></span>
						<span>الإسم الأول <span class="text-red-500">*</span></span>
					</div>
				</label>
				<input
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					id="firstName"
					name="firstName"
					aria-invalid={$errors.firstName ? 'true' : undefined}
					type="text"
					bind:value={$form.firstName}
					{...$constraints.firstName}
					placeholder="Enter your first Name"
					required
				/>
				{#if $errors.firstName}<span class="invalid">{$errors.firstName}</span>{/if}
			</div>

			<!-- Last Name -->
			<div class="mb-4">
				<label class="mb-2 block font-semibold text-gray-700" for="lastName">
					<div class="flex items-center justify-between">
						<span>Last Name <span class="text-red-500">*</span></span>
						<span>الإسم الأخير <span class="text-red-500">*</span></span>
					</div>
				</label>
				<input
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					id="lastName"
					name="lastName"
					aria-invalid={$errors.lastName ? 'true' : undefined}
					type="text"
					bind:value={$form.lastName}
					{...$constraints.lastName}
					placeholder="Enter your last Name"
					required
				/>
				{#if $errors.lastName}<span class="invalid">{$errors.lastName}</span>{/if}
			</div>

			<!-- Email -->
			<div class="mb-4">
				<label class="mb-2 block font-semibold text-gray-700" for="email"
					><div class="flex items-center justify-between">
						<span>Email <span class="text-red-500">*</span></span>
						<span>البريد الإلكتروني <span class="text-red-500">*</span></span>
					</div>
				</label>
				<input
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					id="email"
					name="email"
					type="email"
					aria-invalid={$errors.email ? 'true' : undefined}
					bind:value={$form.email}
					{...$constraints.email}
					placeholder="Enter your email"
					required
				/>
				{#if $errors.email}<span class="invalid">{$errors.email}</span>{/if}
			</div>

			<!-- Mobile -->
			<div class="mb-4">
				<label class="mb-2 block font-semibold text-gray-700" for="phone">
					<div class="flex items-center justify-between">
						<span>Phone Number <span class="text-red-500">*</span></span>
						<span>رقم الهاتف<span class="text-red-500">*</span></span>
					</div>
				</label>
				<input
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					id="mobile"
					name="mobile"
					type="tel"
					placeholder="Enter your mobile number"
					aria-invalid={$errors.mobile ? 'true' : undefined}
					bind:value={$form.mobile}
					{...$constraints.mobile}
				/>
				{#if $errors.mobile}<span class="invalid">{$errors.mobile}</span>{/if}
			</div>

			<!-- Gender -->
			<div class="my-4">
				<label class="mb-2 block font-semibold text-gray-700" for="gender"
					><div class="flex items-center justify-between">
						<span>Gender <span class="text-red-500">*</span></span>
						<span>الجنس <span class="text-red-500">*</span></span>
					</div></label
				>
				<select
					name="gender"
					id="gender"
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					aria-invalid={$errors.gender ? 'true' : undefined}
					bind:value={$form.gender}
					{...$constraints.gender}
				>
					<option value="male">Male - ذكر </option>
					<option value="female">Female - أنثى </option>
				</select>
				{#if $errors.gender}<span class="invalid">{$errors.gender}</span>{/if}
			</div>

			<!-- Nationality  -->
			<!-- <div class="my-4">
				<label class="mb-2 block font-semibold text-gray-700" for="nationality"
					><div class="flex items-center justify-between">
						<span>Nationality <span class="text-red-500">*</span></span>
						<span>الجنسية <span class="text-red-500">*</span></span>
					</div></label
				>
				<select
					name="nationality"
					id="nationality"
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					aria-invalid={$errors.nationality ? 'true' : undefined}
					bind:value={$form.nationality}
					{...$constraints.nationality}
				>
					<option value="qatari">Qatari</option>
					<option value="nonqatari">Non-Qatari</option>
				</select>
				{#if $errors.nationality}<span class="invalid">{$errors.nationality}</span>{/if}
			</div> -->

			<!-- Do you have a Business  -->
			<div class="my-4">
				<label class="mb-2 block font-semibold text-gray-700" for="business"
					><div class="flex items-center justify-between">
						<span>Do you have a business? <span class="text-red-500">*</span></span>
						<span>هل لديك مشروع قائم <span class="text-red-500">*</span></span>
					</div></label
				>
				<select
					name="business"
					id="business"
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					aria-invalid={$errors.business ? 'true' : undefined}
					bind:value={$form.business}
					{...$constraints.business}
				>
					<option value="yes">Yes - نعم</option>
					<option value="no">No - لا</option>
				</select>
				{#if $errors.business}<span class="invalid">{$errors.business}</span>{/if}
			</div>

			<!-- Category   -->
			<div class="my-4">
				<label class="mb-2 block font-semibold text-gray-700" for="category"
					><div class="flex items-center justify-between">
						<span>Category <span class="text-red-500">*</span></span>
						<span>الفئة <span class="text-red-500">*</span></span>
					</div></label
				>
				<select
					name="category"
					id="category"
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					aria-invalid={$errors.category ? 'true' : undefined}
					bind:value={$form.category}
					{...$constraints.category}
				>
					<option value="individual">Individual Entrepreneur - رائد أعمال مستقل</option>
					<option value="founder">Founder/Owner of SME - مؤسس أو شريك بشركة صغيرة أو متوسطة</option>
					<option value="angel">Business angel investor - مستثمر ملائكي</option>
					<option value="employee">Employee of SME - موظف بشركة صغيرة أو متوسطة</option>
				</select>
				{#if $errors.category}<span class="invalid">{$errors.category}</span>{/if}
			</div>

			<!-- Organization -->
			<div class="my-4">
				<label class="mb-2 block font-semibold text-gray-700" for="organization"
					><div class="flex items-center justify-between">
						<span>Business / Company name</span>
						<span>اسم المشروع أو الشركة </span>
					</div></label
				>
				<input
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					id="organization"
					name="organization"
					type="text"
					aria-invalid={$errors.organization ? 'true' : undefined}
					bind:value={$form.organization}
					{...$constraints.organization}
					placeholder="Enter your business or company name"
				/>
				{#if $errors.organization}<span class="invalid">{$errors.organization}</span>{/if}
			</div>

			<!-- Business stage   -->
			<div class="my-4">
				<label class="mb-2 block font-semibold text-gray-700" for="stage"
					><div class="flex items-center justify-between">
						<span>Business Stage <span class="text-red-500">*</span></span>
						<span>مرحلة المشروع <span class="text-red-500">*</span></span>
					</div></label
				>
				<select
					name="stage"
					id="stage"
					class="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
					aria-invalid={$errors.stage ? 'true' : undefined}
					bind:value={$form.stage}
					{...$constraints.stage}
				>
					<option value="na">Not Applicable</option>
					<option value="idea">Idea Stage - مرحلة الفكرة </option>
					<option value="micro">Micro Business - مشروع منزلي / متناهي الصغر </option>
					<option value="startup">Startup - مشروع ناشئ </option>
					<option value="sme">SME - مؤسسة صغيرة أو متوسطة </option>
				</select>
				{#if $errors.stage}<span class="invalid">{$errors.stage}</span>{/if}
			</div>

			<!-- How did you know about this workshop   -->
			<div class="my-4">
				<label class="mb-2 block font-semibold text-gray-700">
					<div class="flex items-center justify-between">
						<span>How did you know about this workshop <span class="text-red-500">*</span></span>
						<span>من أين عرفت عن هذه الورشة <span class="text-red-500">*</span></span>
					</div>
				</label>
				<!-- 
				<div class="space-y-2">
					<label class="flex items-center">
						<input
							type="radio"
							name="know"
							value="social-media"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>Social Media (Instagram, Twitter, Facebook)</span>
					</label>

					<label class="flex items-center">
						<input
							type="radio"
							name="know"
							value="email"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>Email</span>
					</label>

					<label class="flex items-center">
						<input
							type="radio"
							name="know"
							value="friend"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>Friend </span>
					</label>

					<label class="flex items-center">
						<input
							type="radio"
							name="know"
							value="whatsapp"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>WhatsApp </span>
					</label>
					<label class="flex items-center">
						<input
							type="radio"
							name="know"
							value="qdb-employee"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>QDB Employee </span>
					</label>
				</div> -->
				<div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
					<label class="flex items-center rounded border p-2 hover:bg-gray-50">
						<input
							type="radio"
							name="know"
							value="social-media"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span
							>Social Media (Instagram, Twitter, Facebook) - مواقع التواصل الاجتماعي (انستقرام ،
							تويتر، فيسبوك)</span
						>
					</label>

					<label class="flex items-center rounded border p-2 hover:bg-gray-50">
						<input
							type="radio"
							name="know"
							value="email"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>Email - الايميل</span>
					</label>

					<label class="flex items-center rounded border p-2 hover:bg-gray-50">
						<input
							type="radio"
							name="know"
							value="friend"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>Friend - الاصدقاء</span>
					</label>

					<label class="flex items-center rounded border p-2 hover:bg-gray-50">
						<input
							type="radio"
							name="know"
							value="whatsapp"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>WhatsApp - الوتساب</span>
					</label>

					<label class="flex items-center rounded border p-2 hover:bg-gray-50">
						<input
							type="radio"
							name="know"
							value="qdb-employee"
							bind:group={$form.know}
							class="mr-2"
							{...$constraints.know}
						/>
						<span>Employee </span>
					</label>
				</div>

				{#if $errors.know}
					<span class="mt-1 text-sm text-red-500">{$errors.know}</span>
				{/if}
			</div>

			<!-- Form disclaimer -->

			<!-- Checkbox -->
			<div class="flex flex-col items-start space-y-4">
				<label class="flex items-center space-x-2">
					<input
						type="checkbox"
						bind:checked={agreed}
						class="form-checkbox h-5 w-5 text-blue-600"
					/>
					<span class="text-sm text-gray-700">
						By registering you agree to our terms and conditions and privacy policy.
					</span>
				</label>
			</div>

			<Turnstile
				theme="light"
				class="mt-4"
				siteKey={PUBLIC_SITE_KEY}
				on:verify={handleTurnstileVerification}
				on:error={handleTurnstileError}
			/>

			<!-- Button -->
			<div class="mb-4 mt-5 flex items-center justify-center">
				{#if $delayed}
					<button
						class="focus:shadow-outline rounded bg-[#10508E] px-4 py-2 text-white hover:bg-gray-800 focus:outline-none"
						type="submit"
						disabled
					>
						<Loader />
					</button>
				{:else}
					<button
						class="focus:shadow-outline rounded bg-[#10508E] px-4 py-2 text-white hover:bg-gray-800 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
						type="submit"
						disabled={!isFormSubmittable}
					>
						Register
					</button>
				{/if}
			</div>
			<p class="mt-3 text-sm"><span class="text-red-500">* </span>Required</p>
		</form>
	</div>
	<Footer />
</main>

<Toaster />

<style>
	.invalid {
		color: red;
	}
</style>
