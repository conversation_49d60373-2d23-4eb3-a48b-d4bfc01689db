import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load = (async ({ locals: { supabase, safeGetSession }, params }) => {
	const { session } = await safeGetSession();
	if (!session) {
		redirect(303, '/auth');
	}

	console.log('Passed Params id: ', params.eventid);

	// Count total registrations
	const { data: totalRegistrations, error: registrationsError } = await supabase
		.from('guests')
		.select('id', { count: 'exact' })
		.eq('event_id', params.eventid);

	if (registrationsError) {
		console.error('Error fetching event Total count:', registrationsError);
		// Handle the error appropriately, e.g., display an error message to the user
	}

	// Checked-In count
	const { data: eventCheckInCount, error: eventCheckInCountError } = await supabase
		.from('guests')
		.select('id', { count: 'exact' })
		.eq('event_id', params.eventid)
		.eq('status', 'Checked-In');

	if (eventCheckInCountError) {
		console.error('Error fetching event Ceck-In count:', eventCheckInCountError);
		// Handle the error appropriately, e.g., display an error message to the user
	}


	// Checked-out count
	const { data: eventCheckOutCount, error: eventCheckOutCountError } = await supabase
		.from('guests')
		.select('id', { count: 'exact' })
		.eq('event_id', params.eventid)
		.eq('status', 'Checked-Out');

	if (eventCheckOutCountError) {
		console.error('Error fetching event Check-Out count:', eventCheckOutCountError);
		// Handle the error appropriately, e.g., display an error message to the user
	}

	// console.log('Events Data: ', eventsData);
	// console.log('Events Data Error: ', eventsDataError);

	return { eventCheckInCount, eventCheckOutCount, totalRegistrations };
}) satisfies PageServerLoad;