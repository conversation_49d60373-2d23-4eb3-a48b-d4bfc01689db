import type { PageServerLoad } from './$types';
import { generateUniqueId, generateReferenceCode } from '$lib/utils/generateId';

export const load: PageServerLoad = async () => {
    // Generate some examples on the server side
    const serverExamples = {
        serverBasicId: generateUniqueId(),
        serverReferenceCode: generateReferenceCode('SERVER')
    };
    
    return {
        serverExamples
    };
};
