import type { PageServerLoad } from './$types';
import { error, redirect } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, locals: { supabase }, url }) => {
    // Get the event data based on the slug
    const { data: eventArray, error: eventsError } = await supabase
        .from('events')
        .select('id, title, long_description, image_url, start_date, end_date, start_time, end_time, location, location_url')
        .eq('title_slug', params.slug);

    if (eventsError) {
        console.error('Error Querying Events Data: ', eventsError);
        throw error(500, 'Event not found');
    }

    if (!eventArray || eventArray.length === 0) {
        throw error(404, 'Event not found');
    }

    const event = eventArray[0];

    // Load the Share Image and Text
    const { data: socialShare, error: socialShareError } = await supabase
        .from('social_shares')
        .select('share_image_url, share_text')
        .eq('event_id', event.id)
        .single();

    // Check if this is a direct access (without registration)
    // If there's no registration data in the URL, redirect to the event page
    const email = url.searchParams.get('email');
    const registered = url.searchParams.get('registered');

    if (!email || !registered) {
        // This is a direct access without registration, redirect to event page
        redirect(303, `/${params.slug}`);
    }

    // Format the event data for the thank you page
    const eventData = {
        id: event.id,
        name: event.title,
        date: event.start_date,
        startTime: event.start_time,
        endTime: event.end_time,
        location: event.location,
        locationUrl: event.location_url,
        description: event.long_description,
        imageUrl: event.image_url,
        email: email
    };

    return {
        eventData,
        socialShare,
        slug: params.slug
    };
};
