<script lang="ts">
	import { page } from '$app/stores';
	import EventTabNavigation from '$lib/components/EventTabNavigation.svelte';

	export let data;
</script>

<div class="flex h-full flex-col">
	<!-- Event Tab Navigation -->
	<EventTabNavigation eventId={$page.params.eventid} eventTitle={data.eventTitle} />

	<!-- Page Content -->
	<div class="flex-1 overflow-y-auto p-6">
		<slot />
	</div>
</div>
