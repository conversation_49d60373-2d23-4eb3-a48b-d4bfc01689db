import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load = (async ({ locals: { supabase, safeGetSession }, params }) => {
    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/auth');
    }

    console.log('Passed Params id: ', params.eventid);
    const id = '5be8e745-2723-47e3-94e7-0ef2006bfdab'
    // Count total registrations
    const { data: totalRegistrations, error: registrationsError } = await supabase
        .from('guests')
        .select('id')
        .eq('event_id', id);

    if (registrationsError) {
        console.error('Error fetching event Total count:', registrationsError);
        // Handle the error appropriately, e.g., display an error message to the user
    }



    // console.log('Events Data: ', eventsData);
    // console.log('Events Data Error: ', eventsDataError);
    // console.log('Total Registrations: ', totalRegistrations);

    return { totalRegistrations };
}) satisfies PageServerLoad;