<script lang="ts">
	import type { PageData } from './$types';

	export let data: PageData;
</script>

<div class="mx-auto max-w-4xl">
	<h1 class="mb-6 text-2xl font-bold">Available Forms</h1>

	<div class="grid gap-6">
		{#each data.forms as form}
			<div class="rounded-lg bg-white p-6 shadow-md">
				<h2 class="mb-2 text-xl font-semibold">{form.title}</h2>
				{#if form.description}
					<p class="mb-4 text-gray-600">{form.description}</p>
				{/if}
				<a
					href="./forms/{form.id}"
					class="inline-block rounded-md bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
				>
					Fill Out Form
				</a>
			</div>
		{/each}
	</div>
</div>
