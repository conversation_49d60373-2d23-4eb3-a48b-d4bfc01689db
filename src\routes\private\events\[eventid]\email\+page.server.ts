import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from '../$types';
import { supabase } from '$lib/db/supabaseClient';
import { TestEmailContent } from '$lib/server/sendTestTemplate';

export const load = (async ({ locals: { supabase, safeGetSession }, params }) => {

    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/auth');
    }

    const { data: registrationTemplate, error: registrationTemplateError } = await supabase
        .from('email_templates')
        .select(`content, subject, from_name`)
        .eq('event_id', params.eventid).eq('type', 'registration')
        .single();

    if (registrationTemplateError) {
        console.error('Reading Email Template Content Error: ', registrationTemplateError);
    }

    const { data: reminderTemplate, error: reminderTemplateError } = await supabase
        .from('email_templates')
        .select(`content, subject, from_name`)
        .eq('event_id', params.eventid).eq('type', 'reminder')
        .single();

    if (reminderTemplateError) {
        console.error('Reading Reminder Email Template Error: ', reminderTemplateError);
    }



    return { registrationTemplate, reminderTemplate };

}) satisfies PageServerLoad;


export const actions = {
    saveReminder: async ({ request, params, locals }) => {

        console.log('Save Reminder Action Called')

        const data = await request.formData();
        const reminderSender = data.get('reminder-sender') as string;
        const reminderSubject = data.get('reminder-subject') as string;
        const reminderContent = data.get('reminder-content') as string;


        console.log('Email Sender: ', reminderSender);
        console.log('Reminder Subject: ', reminderSubject);
        console.log('Reminder Content: ', reminderContent);

        if (!data.get('reminderSender') || !data.get('reminderSubject') || !data.get('reminderContent')) {
            return fail(400, { message: 'Missing felids' });
        }

        const { data: emailTemplate, error: emailTemplateError } = await supabase
            .from('email_templates')
            .select('content')
            .eq('event_id', params.eventid).select()



        if (emailTemplate?.length === 0) {
            console.log('DATA not found, INSERT the DATA: ');
            const { error: emailTemplateInsertError } = await supabase.from('email_templates').insert({
                content: reminderContent,
                type: 'reminder',
                subject: reminderSubject,
                from_name: reminderSender,
                event_id: params.eventid,
                created_by: locals.user.id,
            })

            if (emailTemplateInsertError) {
                console.error('Error from Event Data Insertion: ', emailTemplateInsertError);
                return fail(500, { message: 'Failed to save email template.' });
            }

        } else {

            console.error('DATA found, UPDATE the DATA: ')
            const { error: emailTemplateUpdateError } = await supabase.from('email_templates').update({
                content: reminderContent,
                type: 'reminder',
                subject: reminderSubject,
                from_name: reminderSender,
                event_id: params.eventid,
                created_by: locals.user.id,
            }).eq('event_id', params.eventid);

            if (emailTemplateUpdateError) {
                console.error('Error from Event Data Insertion: ', emailTemplateUpdateError);
                return fail(500, { message: 'Failed to save email template.' });
            }
        }


        return { success: true };
    },

    test: async ({ params }) => {
        console.log('Test Action Called')

        // Need to add Subject 
        // Need to ask for the email address to send the email to

        const { data, error } = await supabase.from('email_templates').select('content').eq('event_id', params.eventid);


        const processedEmailContent = processTemplate(data[0].content)

        console.log('Processed Email Content: ', processedEmailContent)



        TestEmailContent('Fijo', 'G', '<EMAIL>', processedEmailContent);


    },


    saveRegistration: async ({ request, params, locals }) => {

        console.log('Save Registration Action Called')

        const data = await request.formData();
        const registrationSender = data.get('registration-sender') as string;
        const registrationSubject = data.get('registration-subject') as string;
        const registrationContent = data.get('registration-content') as string;


        if (!registrationSender || !registrationSubject || !registrationContent) {
            console.error('Missing Fields')
            return fail(400, { message: 'Missing felids' });
        }

        const { data: emailTemplate, error: emailTemplateError } = await supabase
            .from('email_templates')
            .select('content')
            .eq('event_id', params.eventid).select()



        if (emailTemplate?.length === 0) {
            console.log('DATA not found, INSERT the DATA: ');
            const { error: emailTemplateInsertError } = await supabase.from('email_templates').insert({
                content: registrationContent,
                type: 'registration',
                subject: registrationSubject,
                from_name: registrationSender,
                event_id: params.eventid,
                created_by: locals.user.id,
            })

            if (emailTemplateInsertError) {
                console.error('Error from Event Data Insertion: ', emailTemplateInsertError);
                return fail(500, { message: 'Failed to save email template.' });
            }

        } else {

            console.error('DATA found, UPDATE the DATA: ')
            const { error: emailTemplateUpdateError } = await supabase.from('email_templates').update({
                content: registrationContent,
                type: 'registration',
                subject: registrationSubject,
                from_name: registrationSender,
                event_id: params.eventid,
                created_by: locals.user.id,
            }).eq('event_id', params.eventid);

            if (emailTemplateUpdateError) {
                console.error('Error from Event Data Insertion: ', emailTemplateUpdateError);
                return fail(500, { message: 'Failed to save email template.' });
            }
        }

        return { success: true };
    },
} satisfies Actions;


function processTemplate(text) {
    let result = text;

    // Hardcoded test values
    const templateValues = {
        firstname: 'John',
        lastname: 'Doe',
        qrlink: 'https://example.com/qr123'
    };

    // Replace each template variable with its corresponding value
    Object.entries(templateValues).forEach(([key, value]) => {
        const template = '${' + key + '}';
        result = result.replaceAll(template, value);
    });

    return result;
}