<script lang="ts">
	import { goto } from '$app/navigation';
	import { But<PERSON> } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table/index.js';
	import type { PageData } from './$types';

	export let data: PageData;
</script>

<headear class="m-4 flex flex-row items-center justify-between">
	<h1 class=" text-4xl font-bold">All Users</h1>
	<Button href="#">Create User</Button>
</headear>

<main>
	<div class="container mx-auto py-10">
		<Table.Root>
			<Table.Caption>A list of all users.</Table.Caption>
			<Table.Header>
				<Table.Row>
					<Table.Head>Name</Table.Head>
					<Table.Head>Email</Table.Head>
					<Table.Head>Mobile No.</Table.Head>
					<Table.Head>Organization</Table.Head>
					<!-- <Table.Head class="text-right">Event Status</Table.Head> -->
				</Table.Row>
			</Table.Header>
			<Table.Body>
				{#each data.usersData as user, i (i)}
					<Table.Row>
						<!-- <Table.Cell class="font-medium"
							><button onclick={() => goto(`/private/users/${user.id}`)} class="text-blue-600"
								>{user.name}</button
							></Table.Cell
						> -->
						<Table.Cell class="font-medium">{user.name}</Table.Cell>
						<Table.Cell>{user.email}</Table.Cell>
						<Table.Cell>{user.mobile}</Table.Cell>
						<Table.Cell>{user.organization}</Table.Cell>
						<!-- <Table.Cell class="text-right">{user.title}</Table.Cell> -->
					</Table.Row>
				{/each}
			</Table.Body>
			<!-- <Table.Footer> -->
			<!-- <Table.Row>
					<Table.Cell colspan={3}>Total</Table.Cell>
					<Table.Cell class="text-right">$2,500.00</Table.Cell>
				</Table.Row> -->
			<!-- </Table.Footer> -->
		</Table.Root>
	</div>
</main>

<!-- <pre>{JSON.stringify(data.eventsData, null, 2)}</pre> -->
