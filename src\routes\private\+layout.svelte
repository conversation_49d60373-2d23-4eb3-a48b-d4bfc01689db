<script>
	import AdminSidebar from '$lib/components/AdminSidebar.svelte';
	import Breadcrumb from '$lib/components/Breadcrumb.svelte';

	export let data;
	$: ({ supabase } = data);
</script>

<div class="flex h-screen w-screen bg-background">
	<!-- Modern Sidebar Navigation -->
	<AdminSidebar />

	<!-- Main Content Area -->
	<main class="flex flex-1 flex-col overflow-hidden">
		<!-- Breadcrumb Header -->
		<header class="border-b bg-background px-6 py-4">
			<Breadcrumb />
		</header>

		<!-- Page Content -->
		<div class="flex-1 overflow-y-auto p-6">
			<slot />
		</div>
	</main>
</div>
