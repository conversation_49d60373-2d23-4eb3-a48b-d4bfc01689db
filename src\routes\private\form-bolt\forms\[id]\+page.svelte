<script lang="ts">
	import { onMount } from 'svelte';

	import type { PageData } from './$types';
	import { supabase } from '$lib/db/supabaseClient';

	export let data: PageData;

	let formValues: Record<string, string> = {};
	let submitted = false;

	async function submitForm() {
		// Create submission
		const { data: submission, error: submissionError } = await supabase
			.from('form_submissions')
			.insert([{ form_id: data.form.id }])
			.select()
			.single();

		if (submissionError) {
			console.error('Error creating submission:', submissionError);
			return;
		}

		// Create submission values
		const submissionValues = data.elements.map((element) => ({
			submission_id: submission.id,
			form_element_id: element.id,
			value: formValues[element.id] || ''
		}));

		const { error: valuesError } = await supabase
			.from('submission_values')
			.insert(submissionValues);

		if (valuesError) {
			console.error('Error saving submission values:', valuesError);
			return;
		}

		submitted = true;
	}
</script>

<div class="mx-auto max-w-2xl">
	<h1 class="mb-2 text-2xl font-bold">{data.form.title}</h1>
	{#if data.form.description}
		<p class="mb-6 text-gray-600">{data.form.description}</p>
	{/if}

	{#if submitted}
		<div class="rounded border border-green-400 bg-green-100 px-4 py-3 text-green-700">
			Thank you for your submission!
		</div>
	{:else}
		<form on:submit|preventDefault={submitForm} class="space-y-6">
			{#each data.elements as element}
				<div>
					<label for={element.id} class="block text-sm font-medium text-gray-700">
						{element.label}
						{#if element.is_required}
							<span class="text-red-600">*</span>
						{/if}
					</label>

					{#if element.help_text}
						<p class="text-sm text-gray-500">{element.help_text}</p>
					{/if}

					{#if element.element_type === 'textarea'}
						<textarea
							id={element.id}
							bind:value={formValues[element.id]}
							placeholder={element.placeholder || ''}
							class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
							required={element.is_required}
						></textarea>
					{:else if element.element_type === 'select'}
						<select
							id={element.id}
							bind:value={formValues[element.id]}
							class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
							required={element.is_required}
						>
							<option value="">Select an option</option>
							{#each element.options as option}
								<option value={option}>{option}</option>
							{/each}
						</select>
					{:else if element.element_type === 'radio'}
						{#each element.options as option}
							<label class="mr-4 mt-2 inline-flex items-center">
								<input
									type="radio"
									name={element.id}
									value={option}
									bind:group={formValues[element.id]}
									required={element.is_required}
									class="form-radio text-indigo-600"
								/>
								<span class="ml-2">{option}</span>
							</label>
						{/each}
					{:else if element.element_type === 'checkbox'}
						{#each element.options as option}
							<label class="mr-4 mt-2 inline-flex items-center">
								<input
									type="checkbox"
									value={option}
									bind:group={formValues[element.id]}
									required={element.is_required}
									class="form-checkbox text-indigo-600"
								/>
								<span class="ml-2">{option}</span>
							</label>
						{/each}
					{:else}
						<input
							type={element.element_type}
							id={element.id}
							bind:value={formValues[element.id]}
							placeholder={element.placeholder || ''}
							class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
							required={element.is_required}
						/>
					{/if}
				</div>
			{/each}

			<button
				type="submit"
				class="rounded-md bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
			>
				Submit
			</button>
		</form>
	{/if}
</div>
