<script lang="ts">
	import * as Card from '$lib/components/ui/card/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Switch } from '$lib/components/ui/switch/index.js';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
</script>

<form class="space-y-8 p-8">
	<Card.Root>
		<Card.Header>
			<Card.Title>Event Settings</Card.Title>
			<Card.Description>Manage your event's registration and attendance settings.</Card.Description>
		</Card.Header>
		<Card.Content class="space-y-6">
			<div class="flex items-center justify-between">
				<Label for="waiting-list">Enable Waiting List</Label>
				<Switch id="waiting-list" />
			</div>

			<div class="flex items-center justify-between">
				<Label for="registration">Enable Registration</Label>
				<Switch id="registration" />
			</div>

			<div class="space-y-2">
				<Label for="registration-status">Registration Status</Label>
				<Select.Root type="single">
					<Select.Trigger>Select status</Select.Trigger>
					<Select.Content>
						<Select.Item value="open">Open</Select.Item>
						<Select.Item value="closed">Closed</Select.Item>
						<Select.Item value="full">Full</Select.Item>
					</Select.Content>
				</Select.Root>
			</div>

			<div class="space-y-2">
				<Label for="qr-scanner">QR Scanner Mode</Label>
				<Select.Root type="single">
					<Select.Trigger>Select mode</Select.Trigger>
					<Select.Content>
						<Select.Item value="automatic">Automatic</Select.Item>
						<Select.Item value="manual">Manual</Select.Item>
					</Select.Content>
				</Select.Root>
			</div>

			<div class="space-y-2">
				<Label for="max-attendees">Maximum Attendees</Label>
				<Input id="max-attendees" type="number" />
			</div>

			<div class="space-y-2">
				<Label for="event-visibility">Event Visibility</Label>
				<Select.Root type="single">
					<Select.Trigger>Select visibility</Select.Trigger>
					<Select.Content>
						<Select.Item value="public">Public</Select.Item>
						<Select.Item value="private">Private</Select.Item>
						<Select.Item value="unlisted">Unlisted</Select.Item>
					</Select.Content>
				</Select.Root>
			</div>

			<div class="flex items-center justify-between">
				<Label for="guest-attendees">Allow Guest Attendees</Label>
				<Switch id="guest-attendees" />
			</div>

			<div class="flex items-center justify-between">
				<Label for="ticket-sales">Enable Ticket Sales</Label>
				<Switch id="ticket-sales" />
			</div>

			<div class="space-y-2">
				<Label for="ticket-types">Number of Ticket Types</Label>
				<Input id="ticket-types" type="number" />
			</div>

			<div class="flex items-center justify-between">
				<Label for="refunds">Enable Refunds</Label>
				<Switch id="refunds" />
			</div>

			<div class="space-y-2">
				<Label for="refund-policy">Refund Policy</Label>
				<Textarea id="refund-policy" placeholder="Enter your refund policy here..." />
			</div>

			<div class="flex items-center justify-between">
				<Label for="live-stream">Enable Live Stream</Label>
				<Switch id="live-stream" />
			</div>

			<div class="flex items-center justify-between">
				<Label for="reminders">Send Reminders</Label>
				<Switch id="reminders" />
			</div>

			<div class="space-y-2">
				<Label for="reminder-frequency">Reminder Frequency</Label>
				<Select.Root type="single">
					<Select.Trigger>Select frequency</Select.Trigger>
					<Select.Content>
						<Select.Item value="hour">Hourly</Select.Item>
						<Select.Item value="day">Daily</Select.Item>
						<Select.Item value="week">Weekly</Select.Item>
					</Select.Content>
				</Select.Root>
			</div>
		</Card.Content>
		<Card.Footer>
			<Button type="submit">Save Settings</Button>
		</Card.Footer>
	</Card.Root>
</form>
