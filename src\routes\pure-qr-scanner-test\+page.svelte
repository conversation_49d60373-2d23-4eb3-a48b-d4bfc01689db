<script lang="ts">
	import { onD<PERSON><PERSON> } from 'svelte';
	import { QrCode, X } from 'lucide-svelte';
	import { BarcodeDetector, type BarcodeFormat } from 'barcode-detector/pure';

	let inputValue = ''; // Stores the scanned QR code value
	let isScanning = false; // Tracks if scanner is active
	let videoElement: HTMLVideoElement; // Reference to video element
	let scanError: string | null = null; // Stores any error messages
	let dialogOpen = false; // Controls modal visibility

	// Track BarcodeDetector instance
	let barcodeDetector: BarcodeDetector;

	onDestroy(() => {
		stopScanner();
	});

	async function startScanner() {
		scanError = null;
		isScanning = true;
		dialogOpen = true;

		try {
			// Create BarcodeDetector instance using the polyfill
			barcodeDetector = new BarcodeDetector({
				formats: ['qr_code'] as BarcodeFormat[]
			});
			// Request camera access
			const stream = await navigator.mediaDevices.getUserMedia({
				video: { facingMode: 'environment' } // Use back camera on mobile
			});
			// Start video stream
			if (videoElement) {
				videoElement.srcObject = stream;
				await videoElement.play();

				// Start scanning for QR codes
				scanQRCode();
			}
		} catch (error) {
			console.error('Error:', error);
			scanError = error instanceof Error ? error.message : 'An error occurred';
			isScanning = false;
		}
	}

	function stopScanner() {
		if (videoElement?.srcObject) {
			const tracks = (videoElement.srcObject as MediaStream).getTracks();
			tracks.forEach((track) => track.stop());
			videoElement.srcObject = null;
		}
		isScanning = false;
		dialogOpen = false;
	}

	async function scanQRCode() {
		if (!videoElement || !isScanning || !barcodeDetector) return;

		try {
			// Attempt to detect QR codes in current video frame
			const codes = await barcodeDetector.detect(videoElement);

			if (codes.length > 0) {
				// QR code found - save value and stop scanner
				inputValue = codes[0].rawValue;
				stopScanner();
			} else {
				// Continue scanning
				requestAnimationFrame(scanQRCode);
			}
		} catch (error) {
			console.error('Error scanning QR code:', error);
			requestAnimationFrame(scanQRCode);
		}
	}

	function handleDialogClose() {
		if (dialogOpen) {
			stopScanner();
		}
	}
</script>

<div class="mx-auto w-full max-w-md">
	<div class="relative">
		<input
			type="text"
			placeholder="Scan QR code or enter text"
			bind:value={inputValue}
			class="w-full rounded-md border border-gray-300 px-4 py-2 pr-10 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
		/>
		<button
			type="button"
			class="absolute right-0 top-0 h-full px-3 text-gray-500 hover:text-gray-900"
			on:click={startScanner}
		>
			<QrCode class="h-5 w-5" />
			<span class="sr-only">Scan QR Code</span>
		</button>
	</div>

	{#if dialogOpen}
		<div
			class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
			on:click={handleDialogClose}
		>
			<div
				class="w-full max-w-md overflow-hidden rounded-lg bg-white shadow-lg"
				on:click|stopPropagation
			>
				<div class="relative">
					<button
						type="button"
						class="absolute right-2 top-2 z-10 rounded-full bg-white/80 p-1 backdrop-blur-sm"
						on:click={stopScanner}
					>
						<X class="h-4 w-4" />
						<span class="sr-only">Close</span>
					</button>

					<div
						class="relative flex aspect-video w-full items-center justify-center overflow-hidden rounded-t-lg bg-black"
					>
						{#if scanError}
							<p class="p-4 text-center text-white">{scanError}</p>
						{:else}
							<video
								bind:this={videoElement}
								class="h-full w-full object-cover"
								playsinline
								muted
								autoplay
							/>
						{/if}
					</div>

					<div class="p-4 text-center">
						<p class="text-sm text-gray-500">Position the QR code within the camera view to scan</p>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
