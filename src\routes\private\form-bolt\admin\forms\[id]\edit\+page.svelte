<script lang="ts">
	import { onMount } from 'svelte';

	import type { PageData } from './$types';
	import { supabase } from '$lib/db/supabaseClient';

	export let data: PageData;

	let form = data.form;
	let elements = data.elements;
	let newElement = {
		element_type: 'text',
		label: '',
		placeholder: '',
		help_text: '',
		is_required: false,
		validation_rules: {},
		options: [],
		order_position: elements.length
	};

	async function addElement() {
		const { data: element, error } = await supabase
			.from('form_elements')
			.insert([{ ...newElement, form_id: form.id }])
			.select()
			.single();

		if (error) {
			console.error('Error adding element:', error);
			return;
		}

		elements = [...elements, element];
		newElement.label = '';
		newElement.placeholder = '';
		newElement.help_text = '';
	}

	async function updateElementOrder(elementId: string, newPosition: number) {
		const { error } = await supabase
			.from('form_elements')
			.update({ order_position: newPosition })
			.eq('id', elementId);

		if (error) {
			console.error('Error updating element order:', error);
		}
	}

	function handleDragStart(e: DragEvent, index: number) {
		e.dataTransfer?.setData('text/plain', index.toString());
	}

	function handleDrop(e: DragEvent, targetIndex: number) {
		e.preventDefault();
		const sourceIndex = parseInt(e.dataTransfer?.getData('text/plain') || '0');
		const element = elements[sourceIndex];

		elements.splice(sourceIndex, 1);
		elements.splice(targetIndex, 0, element);
		elements = elements;

		// Update order positions in database
		elements.forEach((element, index) => {
			updateElementOrder(element.id, index);
		});
	}

	function handleDragOver(e: DragEvent) {
		e.preventDefault();
	}
</script>

<div class="mx-auto max-w-4xl">
	<h1 class="mb-6 text-2xl font-bold">Edit Form: {form.title}</h1>

	<div class="mb-8 rounded-lg bg-white p-6 shadow-md">
		<h2 class="mb-4 text-xl font-semibold">Add Form Element</h2>
		<form on:submit|preventDefault={addElement} class="space-y-4">
			<div class="grid grid-cols-2 gap-4">
				<div>
					<label for="elementType" class="block text-sm font-medium text-gray-700"
						>Element Type</label
					>
					<select
						id="elementType"
						bind:value={newElement.element_type}
						class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
					>
						<option value="text">Text</option>
						<option value="textarea">Textarea</option>
						<option value="number">Number</option>
						<option value="email">Email</option>
						<option value="select">Select</option>
						<option value="radio">Radio</option>
						<option value="checkbox">Checkbox</option>
					</select>
				</div>
				<div>
					<label for="label" class="block text-sm font-medium text-gray-700">Label</label>
					<input
						type="text"
						id="label"
						bind:value={newElement.label}
						class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
						required
					/>
				</div>
			</div>
			<div class="grid grid-cols-2 gap-4">
				<div>
					<label for="placeholder" class="block text-sm font-medium text-gray-700"
						>Placeholder</label
					>
					<input
						type="text"
						id="placeholder"
						bind:value={newElement.placeholder}
						class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
					/>
				</div>
				<div>
					<label for="helpText" class="block text-sm font-medium text-gray-700">Help Text</label>
					<input
						type="text"
						id="helpText"
						bind:value={newElement.help_text}
						class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
					/>
				</div>
			</div>
			<div>
				<label class="flex items-center">
					<input
						type="checkbox"
						bind:checked={newElement.is_required}
						class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
					/>
					<span class="ml-2 text-sm text-gray-700">Required</span>
				</label>
			</div>
			<button
				type="submit"
				class="rounded-md bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
			>
				Add Element
			</button>
		</form>
	</div>

	<div class="rounded-lg bg-white p-6 shadow-md">
		<h2 class="mb-4 text-xl font-semibold">Form Elements</h2>
		<div class="space-y-4">
			{#each elements as element, index}
				<div
					class="cursor-move rounded-md border p-4"
					draggable="true"
					on:dragstart={(e) => handleDragStart(e, index)}
					on:drop={(e) => handleDrop(e, index)}
					on:dragover={handleDragOver}
				>
					<div class="flex items-center justify-between">
						<div>
							<h3 class="text-lg font-medium">{element.label}</h3>
							<p class="text-gray-600">Type: {element.element_type}</p>
							{#if element.placeholder}
								<p class="text-gray-600">Placeholder: {element.placeholder}</p>
							{/if}
							{#if element.help_text}
								<p class="text-gray-600">Help: {element.help_text}</p>
							{/if}
							{#if element.is_required}
								<span class="text-sm text-red-600">Required</span>
							{/if}
						</div>
					</div>
				</div>
			{/each}
		</div>
	</div>
</div>
