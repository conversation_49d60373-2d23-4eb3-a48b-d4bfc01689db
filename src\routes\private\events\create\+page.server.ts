import { redirect } from '@sveltejs/kit';
import { formatDate as formatDisplayDate, formatTime } from '$lib/utils/formatdatetime.js';

import type { Actions } from './$types';


export const actions: Actions = {
    default: async ({ request, locals: { supabase, safeGetSession } }) => {
        // console.log('Actions Triggered. Display request: ', request);
        const { session } = await safeGetSession();

        // console.log('Session: ', session);
        if (!session) {
            redirect(303, '/');
        }

        async function slugify(title: string) {
            return title
                .toLowerCase()
                .trim()
                .replace(/[^a-z0-9\s-]/g, '') // Remove non-alphanumeric characters
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/-+/g, '-'); // Remove duplicate hyphens
        }

        async function createUniqueSlug(title: string) {
            let slugFoundInDB = false;

            let slug = await slugify(title);

            let { data, error } = await supabase
                .from('events')
                .select('title_slug')
                .eq('title_slug', slug);

            // console.log('Slug found in DB need to create a new one: ', data);
            // console.log('Slug found in DB need to create a new one: ', data?.length);

            console.log('Starting the while loop');

            if (data?.length > 0) {
                slugFoundInDB = true;
            }


            let count = 1;
            while (slugFoundInDB) {
                slug = `${slug}-${count}`;
                count++;
                console.log('Count: ', count)

                let { data: slugFoundData } = await supabase
                    .from('events')
                    .select('title_slug')
                    .eq('title_slug', slug);

                console.log('Data from inside the while loop -- slugFoundData: ', slugFoundData);

                if (slugFoundData?.length === 0) {
                    console.log('Slug not found in DB. exiting the loop')
                    slugFoundInDB = false;
                }
            }
            console.log('Outside the while loop');

            if (error) {
                console.error('Error from Event Slug Creation: ', error);
                redirect(303, '/private/events');
            }
            // console.log('Slug from the Function: ', slug);
            return slug;
        }




        const formData = await request.formData();
        console.log('Form Data: ', formData);
        const eventName = formData.get('title') as string;
        const shortDescription = formData.get('shortDescription') as string;
        const longDescription = formData.get('longDescription') as string;
        const longDescriptionArabic = formData.get('longDescriptionArabic') as string;
        const startDate = formData.get('startDate') as string;
        const startTime = formData.get('startTime') as string;
        const endDate = formData.get('endDate') as string;
        const endTime = formData.get('endTime') as string;
        const location = formData.get('location') as string;
        const locationUrl = formData.get('locationUrl') as string;
        const maxRegistrations = formData.get('maxParticipants') as string;
        const bannerFile = formData.get('banner') as File;
        const slug = formData.get('slug') as string;

        const friendlySlug = await createUniqueSlug(eventName);
        console.log('Slug from the Action: ', friendlySlug);

        // Upload banner if exists
        let bannerUrl = null;
        if (bannerFile && bannerFile.size > 0) {
            const fileExt = bannerFile.name.split('.').pop();
            const fileName = `${Date.now()}.${fileExt}`;
            // const filePath = `event-banners/${fileName}`;


            const { error: uploadError } = await supabase.storage
                .from('event-banners')
                .upload(fileName, bannerFile);

            if (uploadError) {
                console.error('Failed to upload banner', uploadError);
                return
            }

            const { data: { publicUrl }, error: publicUrlError } = supabase.storage
                .from('event-banners')
                .getPublicUrl(fileName);

            if (publicUrlError) {
                console.error('Error in Getting Public URL: ', publicUrlError);
            }

            console.log('Public URL: ', publicUrl);

            bannerUrl = publicUrl;
        }



        console.log('Event Form Data: ', formData);
        const { error, data: eventInsertData } = await supabase.from('events').insert({
            title: eventName,
            short_description: shortDescription,
            long_description: longDescription,
            long_description_arabic: longDescriptionArabic,
            start_date: startDate,
            start_time: startTime,
            end_date: endDate,
            end_time: endTime,
            max_registrations: maxRegistrations ? parseInt(maxRegistrations) : null,
            location: location,
            location_url: locationUrl,
            image_url: bannerUrl,
            is_published: true,
            created_by: session.user.id,
            title_slug: friendlySlug,
        }).select('id, title, start_date, location');
        console.log('Event Data Inserted.', eventInsertData);



        const eventId = eventInsertData[0].id;
        console.log('Event ID: ', eventId);

        let shareText = `I'm excited to attend ${eventInsertData[0].title} on ${formatDisplayDate(eventInsertData[0].start_date)}! Join me at ${eventInsertData[0].location} for an amazing experience. #${eventInsertData[0].title}`;
        console.log('Share Text: ', shareText);

        const { error: socialShareError } = await supabase.from('social_shares').insert({
            share_text: shareText,
            event_id: eventId,
            share_image_url: 'https://kohmauxkxddyfgvkatmf.supabase.co/storage/v1/object/public/event-share-images//eharisshare.png'
        })

        if (socialShareError) {
            console.error('Error from Social Share Insertion: ', socialShareError);
            redirect(303, '/private/events');
        }

        if (error) {
            console.error('Error from Event Data Insertion: ', error);
            redirect(303, '/private/events');
        } else {
            redirect(303, `/private/events`);
        }
    },
};


