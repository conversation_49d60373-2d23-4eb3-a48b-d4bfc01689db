<script lang="ts">
	import { page } from '$app/stores';
	import { ChevronRight, Home } from 'lucide-svelte';

	interface BreadcrumbItem {
		label: string;
		href?: string;
		isActive?: boolean;
	}

	let breadcrumbs = $derived(generateBreadcrumbs($page.url.pathname));

	function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
		const segments = pathname.split('/').filter(Boolean);
		const breadcrumbs: BreadcrumbItem[] = [];

		// Always start with Home/Dashboard
		breadcrumbs.push({
			label: 'Dashboard',
			href: '/private/dashboard'
		});

		let currentPath = '';
		
		for (let i = 0; i < segments.length; i++) {
			const segment = segments[i];
			currentPath += `/${segment}`;
			
			// Skip 'private' segment as it's not user-facing
			if (segment === 'private') continue;
			
			let label = formatSegmentLabel(segment, segments, i);
			let href = currentPath;
			
			// Don't make the last item clickable
			if (i === segments.length - 1) {
				href = undefined;
			}
			
			breadcrumbs.push({
				label,
				href,
				isActive: i === segments.length - 1
			});
		}

		return breadcrumbs;
	}

	function formatSegmentLabel(segment: string, segments: string[], index: number): string {
		// Handle specific route patterns
		switch (segment) {
			case 'events':
				return 'Events';
			case 'users':
				return 'Users';
			case 'form-bolt':
				return 'Forms';
			case 'admin':
				return 'Admin';
			case 'forms':
				return 'Forms';
			case 'create':
				return 'Create';
			case 'edit':
				return 'Edit';
			case 'guestlist':
				return 'Guest List';
			case 'email':
				return 'Email';
			case 'scanner':
				return 'Scanner';
			case 'social-share':
				return 'Social Share';
			case 'settings':
				return 'Settings';
			case 'analytics':
				return 'Analytics';
			case 'templates':
				return 'Templates';
			case 'integrations':
				return 'Integrations';
			case 'email-templates':
				return 'Email Templates';
			default:
				// Check if it's a UUID or ID (for dynamic routes)
				if (segment.match(/^[a-f0-9-]{36}$/) || segment.match(/^\d+$/)) {
					// Try to get a more meaningful name based on context
					const prevSegment = segments[index - 1];
					if (prevSegment === 'events') {
						return 'Event Details';
					} else if (prevSegment === 'users') {
						return 'User Details';
					} else if (prevSegment === 'forms') {
						return 'Form Details';
					}
					return 'Details';
				}
				// Capitalize and replace hyphens with spaces
				return segment
					.split('-')
					.map(word => word.charAt(0).toUpperCase() + word.slice(1))
					.join(' ');
		}
	}
</script>

<nav class="flex items-center space-x-1 text-sm text-muted-foreground" aria-label="Breadcrumb">
	<ol class="flex items-center space-x-1">
		{#each breadcrumbs as item, index}
			<li class="flex items-center">
				{#if index > 0}
					<ChevronRight class="mx-2 h-4 w-4" />
				{/if}
				
				{#if item.href}
					<a
						href={item.href}
						class="hover:text-foreground transition-colors"
					>
						{#if index === 0}
							<Home class="h-4 w-4" />
						{:else}
							{item.label}
						{/if}
					</a>
				{:else}
					<span class="text-foreground font-medium">
						{#if index === 0}
							<Home class="h-4 w-4" />
						{:else}
							{item.label}
						{/if}
					</span>
				{/if}
			</li>
		{/each}
	</ol>
</nav>
