import type { PageLoad } from '../private/events/create/[eventid]/$types';

export const load = (async ({ data }) => {
    console.log('-----------------------------------------')
    console.log('Total Registrations from Page.ts: ', data);
    console.log('-----------------------------------------')
    const processedData = data.totalRegistrations[0];
    console.log('Processed Data: ', processedData);
    return {
        status: 200,
        props: {
            processedData
        }
    };
}) satisfies PageLoad;