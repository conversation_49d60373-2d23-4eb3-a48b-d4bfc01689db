<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { ChevronDown, ChevronRight, Menu, X, LogOut } from 'lucide-svelte';
	import { mainNavigation, shouldExpandNavItem } from '$lib/config/navigation';
	import type { NavigationItem } from '$lib/config/navigation';
	import { supabase } from '$lib/db/supabaseClient';

	let isCollapsed = $state(false);
	let expandedItems = $state(new Set<string>());

	// Initialize expanded items based on current path
	$effect(() => {
		const pathname = $page.url.pathname;
		const newExpanded = new Set<string>();

		mainNavigation.forEach((item) => {
			if (item && item.id && shouldExpandNavItem(pathname, item)) {
				newExpanded.add(item.id);
			}
		});

		expandedItems = newExpanded;
	});

	function toggleSidebar() {
		isCollapsed = !isCollapsed;
	}

	function toggleExpanded(itemId: string) {
		const newExpanded = new Set(expandedItems);
		if (newExpanded.has(itemId)) {
			newExpanded.delete(itemId);
		} else {
			newExpanded.add(itemId);
		}
		expandedItems = newExpanded;
	}

	function isItemActive(item: NavigationItem): boolean {
		const pathname = $page.url.pathname;
		if (item.href === pathname) return true;
		if (item.children) {
			return item.children.some((child) => isItemActive(child));
		}
		return false;
	}

	function isExactMatch(href: string): boolean {
		return $page.url.pathname === href;
	}

	async function logout() {
		const { error } = await supabase.auth.signOut();
		if (error) {
			console.error('Error logging out:', error);
		} else {
			goto('/auth');
		}
	}
</script>

<aside
	class="flex h-full flex-col border-r bg-sidebar transition-all duration-300 {isCollapsed
		? 'w-16'
		: 'w-64'}"
>
	<!-- Header -->
	<div class="flex h-16 items-center justify-between border-b px-4">
		{#if !isCollapsed}
			<h2 class="text-lg font-semibold text-sidebar-foreground">Admin Panel</h2>
		{/if}
		<button
			onclick={toggleSidebar}
			class="rounded-md p-2 hover:bg-sidebar-accent"
			aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
		>
			{#if isCollapsed}
				<Menu class="h-4 w-4" />
			{:else}
				<X class="h-4 w-4" />
			{/if}
		</button>
	</div>

	<!-- Navigation -->
	<nav class="flex-1 overflow-y-auto p-4">
		<ul class="space-y-2">
			{#each mainNavigation as item}
				{#if item && item.id}
					<li>
						{#if item.children}
							<!-- Parent item with children -->
							<div>
								<button
									onclick={() => toggleExpanded(item.id)}
									class="flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground {isItemActive(
										item
									)
										? 'bg-sidebar-accent text-sidebar-accent-foreground'
										: 'text-sidebar-foreground'}"
								>
									<div class="flex items-center gap-3">
										{#if item.icon}
											{@const Icon = item.icon}
											<Icon class="h-4 w-4 flex-shrink-0" />
										{/if}
										{#if !isCollapsed}
											<span>{item.label}</span>
										{/if}
									</div>
									{#if !isCollapsed}
										{#if expandedItems.has(item.id)}
											<ChevronDown class="h-4 w-4" />
										{:else}
											<ChevronRight class="h-4 w-4" />
										{/if}
									{/if}
								</button>

								{#if expandedItems.has(item.id) && !isCollapsed}
									<ul class="ml-6 mt-2 space-y-1 border-l border-sidebar-border pl-4">
										{#each item.children as child}
											<li>
												<a
													href={child.href}
													class="flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground {isExactMatch(
														child.href || ''
													)
														? 'bg-sidebar-primary text-sidebar-primary-foreground'
														: 'text-sidebar-foreground'}"
												>
													{#if child.icon}
														{@const ChildIcon = child.icon}
														<ChildIcon class="h-3 w-3 flex-shrink-0" />
													{/if}
													<span>{child.label}</span>
													{#if child.badge}
														<span
															class="ml-auto rounded-full bg-sidebar-primary px-2 py-0.5 text-xs text-sidebar-primary-foreground"
														>
															{child.badge}
														</span>
													{/if}
												</a>
											</li>
										{/each}
									</ul>
								{/if}
							</div>
						{:else}
							<!-- Single item without children -->
							<a
								href={item.href}
								class="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground {isExactMatch(
									item.href || ''
								)
									? 'bg-sidebar-primary text-sidebar-primary-foreground'
									: 'text-sidebar-foreground'}"
							>
								{#if item.icon}
									{@const ItemIcon = item.icon}
									<ItemIcon class="h-4 w-4 flex-shrink-0" />
								{/if}
								{#if !isCollapsed}
									<span>{item.label}</span>
								{/if}
								{#if item.badge && !isCollapsed}
									<span
										class="ml-auto rounded-full bg-sidebar-primary px-2 py-0.5 text-xs text-sidebar-primary-foreground"
									>
										{item.badge}
									</span>
								{/if}
							</a>
						{/if}
					</li>
				{/if}
			{/each}
		</ul>
	</nav>

	<!-- Footer -->
	<div class="border-t p-4">
		<button
			onclick={logout}
			class="flex w-full items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-sidebar-foreground transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
		>
			<LogOut class="h-4 w-4 flex-shrink-0" />
			{#if !isCollapsed}
				<span>Logout</span>
			{/if}
		</button>
	</div>
</aside>
