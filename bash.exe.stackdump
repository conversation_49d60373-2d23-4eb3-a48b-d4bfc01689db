Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE33170000 ntdll.dll
7FFE31550000 KERNEL32.DLL
7FFE307F0000 KERNELBASE.dll
7FFE32B70000 USER32.dll
7FFE30E70000 win32u.dll
7FFE322E0000 GDI32.dll
7FFE30BB0000 gdi32full.dll
7FFE30720000 msvcp_win.dll
7FFE30D50000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE31230000 advapi32.dll
7FFE31620000 msvcrt.dll
7FFE32AC0000 sechost.dll
7FFE307C0000 bcrypt.dll
7FFE33010000 RPCRT4.dll
7FFE2FD10000 CRYPTBASE.DLL
7FFE30CD0000 bcryptPrimitives.dll
7FFE32320000 IMM32.DLL
