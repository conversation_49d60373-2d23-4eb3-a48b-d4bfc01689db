import { redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase, safeGetSession }, params }) => {
    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/auth');
    }

    console.log('Params: ', params);
    console.log('Params EventID: ', params.eventid);



    const { data: QRData, error } = await supabase
        .from('guests')
        .select('email')
        .eq('event_id', params.eventid)

    if (error) {
        console.error('Error fetching guests data: ', error.message);
        return { error: error.message };
    }


    // console.log('Guests Data from the Server: ', guestsData);
    return { QRData, params };
    // return { data };
};