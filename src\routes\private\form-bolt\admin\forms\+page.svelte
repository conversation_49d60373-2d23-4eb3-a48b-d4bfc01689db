<script lang="ts">
	import { supabase } from '$lib/db/supabaseClient';

	export let data;

	let forms = data.forms;
	let title = '';
	let description = '';

	async function createForm() {
		const { data: newForm, error } = await supabase
			.from('forms')
			.insert([{ title, description }])
			.select()
			.single();

		if (error) {
			console.error('Error creating form:', error);
			return;
		}

		forms = [...forms, newForm];
		title = '';
		description = '';
	}

	async function toggleFormStatus(formId: string, isActive: boolean) {
		const { error } = await supabase
			.from('forms')
			.update({ is_active: !isActive })
			.eq('id', formId);

		if (error) {
			console.error('Error toggling form status:', error);
			return;
		}

		forms = forms.map((form) => (form.id === formId ? { ...form, is_active: !isActive } : form));
	}
</script>

<div class="mx-auto max-w-4xl">
	<h1 class="mb-6 text-2xl font-bold">Manage Forms</h1>

	<div class="mb-8 rounded-lg bg-white p-6 shadow-md">
		<h2 class="mb-4 text-xl font-semibold">Create New Form</h2>
		<form on:submit|preventDefault={createForm} class="space-y-4">
			<div>
				<label for="title" class="block text-sm font-medium text-gray-700">Title</label>
				<input
					type="text"
					id="title"
					bind:value={title}
					class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
					required
				/>
			</div>
			<div>
				<label for="description" class="block text-sm font-medium text-gray-700">Description</label>
				<textarea
					id="description"
					bind:value={description}
					class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
					rows="3"
				></textarea>
			</div>
			<button
				type="submit"
				class="rounded-md bg-indigo-600 px-4 py-2 text-black hover:bg-indigo-700"
			>
				Create Form
			</button>
		</form>
	</div>

	<div class="rounded-lg bg-white p-6 shadow-md">
		<h2 class="mb-4 text-xl font-semibold">Your Forms</h2>
		<div class="space-y-4">
			{#each forms as form}
				<div class="rounded-md border p-4">
					<div class="flex items-center justify-between">
						<div>
							<h3 class="text-lg font-medium">{form.title}</h3>
							<p class="text-gray-600">{form.description}</p>
						</div>
						<div class="space-x-2">
							<a
								href="forms/{form.id}/edit"
								class="inline-block rounded-md bg-blue-600 px-3 py-1 text-white hover:bg-blue-700"
							>
								Edit
							</a>
							<button
								on:click={() => toggleFormStatus(form.id, form.is_active)}
								class="inline-block {form.is_active
									? 'bg-red-600'
									: 'bg-green-600'} rounded-md px-3 py-1 text-white hover:{form.is_active
									? 'bg-red-700'
									: 'bg-green-700'}"
							>
								{form.is_active ? 'Deactivate' : 'Activate'}
							</button>
						</div>
					</div>
				</div>
			{/each}
		</div>
	</div>
</div>
