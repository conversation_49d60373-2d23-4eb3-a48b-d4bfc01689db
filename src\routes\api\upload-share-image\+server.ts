import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    const formData = await request.formData();
    const shareImage = formData.get('shareImage') as File;

    if (!shareImage) {
        return json({ error: 'No image provided' }, { status: 400 });
    }

    try {
        const fileName = `share_${Date.now()}_${shareImage.name}`;

        // Upload to Supabase Storage
        const { error: uploadError } = await supabase.storage
            .from('event-share-images')
            .upload(fileName, shareImage);

        if (uploadError) {
            throw uploadError;
        }

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
            .from('event-share-images')
            .getPublicUrl(fileName);

        return json({ imageUrl: publicUrl });
    } catch (error) {
        console.error('Error uploading image:', error);
        return json({ error: 'Failed to upload image' }, { status: 500 });
    }
};