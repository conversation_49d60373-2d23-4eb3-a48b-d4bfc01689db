<script lang="ts">
	import { generateUniqueId, generateReferenceCode } from '$lib/utils/generateId';
	import { onMount } from 'svelte';

	export let data;

	let examples = {
		basicId: '',
		longId: '',
		prefixedId: '',
		numbersOnlyId: '',
		specialId: '',
		referenceCode: '',
		eventReferenceCode: ''
	};

	let customLength = 10;
	let customPrefix = 'CUSTOM-';
	let useLetters = true;
	let useNumbers = true;
	let useSpecial = false;
	let customId = '';

	function generateCustomId() {
		customId = generateUniqueId({
			length: customLength,
			prefix: customPrefix,
			useLetters,
			useNumbers,
			useSpecial
		});
	}

	onMount(() => {
		// Generate examples on page load
		examples.basicId = generateUniqueId();
		examples.longId = generateUniqueId({ length: 20 });
		examples.prefixedId = generateUniqueId({ prefix: 'EVENT-', length: 8 });
		examples.numbersOnlyId = generateUniqueId({ useLetters: false, useNumbers: true });
		examples.specialId = generateUniqueId({ useSpecial: true });
		examples.referenceCode = generateReferenceCode();
		examples.eventReferenceCode = generateReferenceCode('EVENT');

		// Generate initial custom ID
		generateCustomId();
	});
</script>

<div class="container mx-auto px-4 py-8">
	<h1 class="mb-6 text-3xl font-bold">ID Generator Utility Examples</h1>

	<div class="grid grid-cols-1 gap-8 md:grid-cols-2">
		<div class="rounded-lg border bg-card p-6 shadow-sm">
			<h2 class="mb-4 text-xl font-semibold">Pre-generated Examples</h2>

			<div class="space-y-4">
				<div>
					<p class="text-sm text-muted-foreground">Basic ID (default settings):</p>
					<p class="rounded bg-muted p-2 font-mono">{examples.basicId}</p>
				</div>

				<div>
					<p class="text-sm text-muted-foreground">Long ID (20 characters):</p>
					<p class="rounded bg-muted p-2 font-mono">{examples.longId}</p>
				</div>

				<div>
					<p class="text-sm text-muted-foreground">Prefixed ID:</p>
					<p class="rounded bg-muted p-2 font-mono">{examples.prefixedId}</p>
				</div>

				<div>
					<p class="text-sm text-muted-foreground">Numbers only ID:</p>
					<p class="rounded bg-muted p-2 font-mono">{examples.numbersOnlyId}</p>
				</div>

				<div>
					<p class="text-sm text-muted-foreground">ID with special characters:</p>
					<p class="rounded bg-muted p-2 font-mono">{examples.specialId}</p>
				</div>

				<div>
					<p class="text-sm text-muted-foreground">Reference code:</p>
					<p class="rounded bg-muted p-2 font-mono">{examples.referenceCode}</p>
				</div>

				<div>
					<p class="text-sm text-muted-foreground">Event reference code:</p>
					<p class="rounded bg-muted p-2 font-mono">{examples.eventReferenceCode}</p>
				</div>

				<div class="mt-6 border-t pt-4">
					<h3 class="mb-2 text-lg font-medium">Server-generated Examples</h3>

					<div class="space-y-2">
						<div>
							<p class="text-sm text-muted-foreground">Server Basic ID:</p>
							<p class="rounded bg-muted p-2 font-mono">{data.serverExamples.serverBasicId}</p>
						</div>

						<div>
							<p class="text-sm text-muted-foreground">Server Reference Code:</p>
							<p class="rounded bg-muted p-2 font-mono">
								{data.serverExamples.serverReferenceCode}
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="rounded-lg border bg-card p-6 shadow-sm">
			<h2 class="mb-4 text-xl font-semibold">Generate Custom ID</h2>

			<div class="space-y-4">
				<div>
					<label for="length" class="mb-1 block text-sm font-medium">Length:</label>
					<input
						id="length"
						type="number"
						bind:value={customLength}
						min="1"
						max="50"
						class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring"
					/>
				</div>

				<div>
					<label for="prefix" class="mb-1 block text-sm font-medium">Prefix:</label>
					<input
						id="prefix"
						type="text"
						bind:value={customPrefix}
						class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring"
					/>
				</div>

				<div class="flex space-x-4">
					<label class="flex items-center space-x-2">
						<input type="checkbox" bind:checked={useLetters} class="h-4 w-4" />
						<span>Use Letters</span>
					</label>

					<label class="flex items-center space-x-2">
						<input type="checkbox" bind:checked={useNumbers} class="h-4 w-4" />
						<span>Use Numbers</span>
					</label>

					<label class="flex items-center space-x-2">
						<input type="checkbox" bind:checked={useSpecial} class="h-4 w-4" />
						<span>Use Special</span>
					</label>
				</div>

				<button
					on:click={generateCustomId}
					class="rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90"
				>
					Generate ID
				</button>

				<div class="mt-4">
					<p class="text-sm text-muted-foreground">Generated ID:</p>
					<p class="rounded bg-muted p-2 font-mono">{customId}</p>
				</div>
			</div>
		</div>
	</div>
</div>
