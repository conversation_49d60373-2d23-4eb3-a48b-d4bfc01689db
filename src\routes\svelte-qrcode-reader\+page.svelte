<script lang="ts">
	import { QrCode, X } from 'lucide-svelte';
	import QrcodeStream from '$lib/components/qrcode-stream.svelte';
	import type { DetectedBarcode } from 'barcode-detector/pure';

	let inputValue = '';
	let dialogOpen = false;
	let scanError: string | null = null;
	let paused = false;

	function onDetect(detectedCodes: DetectedBarcode[]) {
		if (detectedCodes.length > 0) {
			inputValue = detectedCodes[0].rawValue;
			dialogOpen = false;
		}
	}

	function onError(error: Error) {
		console.error('Error:', error);
		scanError = error.message;
	}

	function handleDialogClose() {
		if (dialogOpen) {
			dialogOpen = false;
		}
	}

	function handleModalClick(e: Event) {
		e.stopPropagation();
	}
</script>

<div class="mx-auto w-full max-w-md">
	<div class="relative">
		<input
			type="text"
			placeholder="Scan QR code or enter text"
			bind:value={inputValue}
			class="w-full rounded-md border border-gray-300 px-4 py-2 pr-10 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
		/>
		<button
			type="button"
			class="absolute right-0 top-0 h-full px-3 text-gray-500 hover:text-gray-900"
			onclick={() => (dialogOpen = true)}
		>
			<QrCode class="h-5 w-5" />
			<span class="sr-only">Scan QR Code</span>
		</button>
	</div>

	{#if dialogOpen}
		<div
			class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
			onclick={handleDialogClose}
		>
			<div
				class="w-full max-w-md overflow-hidden rounded-lg bg-white shadow-lg"
				onclick={handleModalClick}
			>
				<div class="relative">
					<button
						type="button"
						class="absolute right-2 top-2 z-10 rounded-full bg-white/80 p-1 backdrop-blur-sm"
						onclick={() => (dialogOpen = false)}
					>
						<X class="h-4 w-4" />
						<span class="sr-only">Close</span>
					</button>

					<div class="relative h-[300px] w-full overflow-hidden rounded-t-lg bg-black">
						{#if scanError}
							<p class="p-4 text-center text-white">{scanError}</p>
						{:else}
							<QrcodeStream
								{onDetect}
								{onError}
								{paused}
								constraints={{ facingMode: 'environment' }}
							/>
						{/if}
					</div>

					<div class="p-4 text-center">
						<p class="text-sm text-gray-500">Position the QR code within the camera view to scan</p>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
