import { redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase, safeGetSession }, params }) => {
    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/auth');
    }

    console.log('Params: ', params);
    console.log('Params EventID: ', params.eventid);


    // const { data: guestsData } = await supabase
    //     .from('guests')
    //     .select()
    //     .eq('event_id', params.eventid)

    // const { count } = await supabase.from('guests').select('id', { count: 'exact' }).eq('event_id', params.eventid);

    // optimized query

    const { data: guestsData, count } = await supabase
        .from('guests')
        .select('id, first_name, last_name, email, mobile, organization, status', { count: 'exact' })
        .eq('event_id', params.eventid)



    // console.log('Guests Data from the Server: ', guestsData);
    return { session, guestsData, count, params };
    // return { data };
};


