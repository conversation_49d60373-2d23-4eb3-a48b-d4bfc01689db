<script lang="ts">
	import { Calendar, Users, FileText, BarChart3 } from 'lucide-svelte';
</script>

<div class="space-y-6">
	<div>
		<h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
		<p class="text-muted-foreground">Welcome to your admin dashboard</p>
	</div>

	<!-- Quick Stats -->
	<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
		<div class="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
			<div class="flex items-center justify-between space-y-0 pb-2">
				<h3 class="text-sm font-medium tracking-tight">Total Events</h3>
				<Calendar class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">12</div>
			<p class="text-xs text-muted-foreground">+2 from last month</p>
		</div>

		<div class="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
			<div class="flex items-center justify-between space-y-0 pb-2">
				<h3 class="text-sm font-medium tracking-tight">Total Users</h3>
				<Users class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">1,234</div>
			<p class="text-xs text-muted-foreground">+15% from last month</p>
		</div>

		<div class="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
			<div class="flex items-center justify-between space-y-0 pb-2">
				<h3 class="text-sm font-medium tracking-tight">Active Forms</h3>
				<FileText class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">8</div>
			<p class="text-xs text-muted-foreground">+1 from last week</p>
		</div>

		<div class="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
			<div class="flex items-center justify-between space-y-0 pb-2">
				<h3 class="text-sm font-medium tracking-tight">This Month</h3>
				<BarChart3 class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">2,345</div>
			<p class="text-xs text-muted-foreground">Registrations</p>
		</div>
	</div>

	<!-- Quick Actions -->
	<div class="grid gap-4 md:grid-cols-2">
		<div class="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
			<h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
			<div class="space-y-2">
				<a href="/private/events/create" class="block rounded-md bg-primary px-4 py-2 text-center text-primary-foreground hover:bg-primary/90 transition-colors">
					Create New Event
				</a>
				<a href="/private/form-bolt/admin/forms/create" class="block rounded-md border border-input px-4 py-2 text-center hover:bg-accent hover:text-accent-foreground transition-colors">
					Create New Form
				</a>
			</div>
		</div>

		<div class="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
			<h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
			<div class="space-y-3 text-sm">
				<div class="flex items-center justify-between">
					<span>New user registered</span>
					<span class="text-muted-foreground">2 hours ago</span>
				</div>
				<div class="flex items-center justify-between">
					<span>Event "Summer Conference" updated</span>
					<span class="text-muted-foreground">4 hours ago</span>
				</div>
				<div class="flex items-center justify-between">
					<span>Form "Contact Us" created</span>
					<span class="text-muted-foreground">1 day ago</span>
				</div>
			</div>
		</div>
	</div>
</div>
