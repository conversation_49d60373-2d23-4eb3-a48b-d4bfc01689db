import { redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase, safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/auth');
    }


    const { data: usersData } = await supabase
        .from('users')
        .select()

    console.log('Users Data: ', usersData);
    return { session, usersData };
};

// export const actions: Actions = {
//     delete: async ({ request, locals: { supabase, safeGetSession } }) => {
//         const { session } = await safeGetSession();
//         if (!session) {
//             return;
//         }
//         const formData = await request.formData();
//         console.log('Form Data: ', formData);
//         const eventId = formData.get('eventId') as string;
//         console.log('Delete Action Celled with Event ID: ', eventId);
//     }
// };
